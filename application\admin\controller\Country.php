<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use Exception;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use think\Db;
/**
 * 国家信息管理
 *
 * @icon fa fa-circle-o
 */
class Country extends Backend
{

    /**
     * Country模型对象
     * @var \app\admin\model\Country
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Country;
        $this->view->assign("wlTypeList", $this->model->getWlTypeList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

	public function import()
	{
	    $file = $this->request->request('file');
	    if (!$file) {
	        $this->error(__('Parameter %s can not be empty', 'file'));
	    }
	    $filePath = ROOT_PATH . DS . 'public' . DS . $file;
	    if (!is_file($filePath)) {
	        $this->error(__('No results were found'));
	    }
	    //实例化reader
	    $ext = pathinfo($filePath, PATHINFO_EXTENSION);
	    if (!in_array($ext, ['csv', 'xls', 'xlsx'])) {
	        $this->error(__('Unknown data format'));
	    }
	    if ($ext === 'csv') {
	        $file = fopen($filePath, 'r');
	        $filePath = tempnam(sys_get_temp_dir(), 'import_csv');
	        $fp = fopen($filePath, 'w');
	        $n = 0;
	        while ($line = fgets($file)) {
	            $line = rtrim($line, "\n\r\0");
	            $encoding = mb_detect_encoding($line, ['utf-8', 'gbk', 'latin1', 'big5']);
	            if ($encoding !== 'utf-8') {
	                $line = mb_convert_encoding($line, 'utf-8', $encoding);
	            }
	            if ($n == 0 || preg_match('/^".*"$/', $line)) {
	                fwrite($fp, $line . "\n");
	            } else {
	                fwrite($fp, '"' . str_replace(['"', ','], ['""', '","'], $line) . "\"\n");
	            }
	            $n++;
	        }
	        fclose($file) || fclose($fp);

	        $reader = new Csv();
	    } elseif ($ext === 'xls') {
	        $reader = new Xls();
	    } else {
	        $reader = new Xlsx();
	    }


		 $fieldArr = [
			 '国家简码' => 'sxname',
			 '中文国家名' => 'name',
			 '英文国家名' => 'ename',
			 '发货时效' => 'deliver_days',
		 ];

	    //加载文件
	    $insert = [];
	    try {
	        if (!$PHPExcel = $reader->load($filePath)) {
	            $this->error(__('Unknown data format'));
	        }
	        $currentSheet = $PHPExcel->getSheet(0);  //读取文件中的第一个工作表
	        $allColumn = $currentSheet->getHighestDataColumn(); //取得最大的列号
	        $allRow = $currentSheet->getHighestRow(); //取得一共有多少行
	        $maxColumnNumber = Coordinate::columnIndexFromString($allColumn);
	        $fields = [];
	        for ($currentRow = 1; $currentRow <= 1; $currentRow++) {
	            for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
	                $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
	                $fields[] = $val;
	            }
	        }

	        for ($currentRow = 2; $currentRow <= $allRow; $currentRow++) {
	            $values = [];
	            for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
	                $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
	                $values[] = is_null($val) ? '' : $val;
	            }
	            $row = [];
	            $temp = array_combine($fields, $values);
	            foreach ($temp as $k => $v) {
	                if (isset($fieldArr[$k]) && $k !== '') {
	                    $row[$fieldArr[$k]] = $v;
	                }
	            }
	            if ($row) {
	                $insert[] = $row;
	            }
	        }
	    } catch (Exception $exception) {
	        $this->error($exception->getMessage());
	    }
	    if (!$insert) {
	        $this->error(__('No rows were updated'));
	    }
		// print_r($insert);exit;
	    try {
	        //是否包含admin_id字段
			foreach($insert as &$row){
				$country_row = $this->model->where('name',$row['name'])->find();
				if($country_row){
					$row['id'] = $country_row['id'];
				}
			}
	        $this->model->saveAll($insert);
	    } catch (PDOException $exception) {
	        $msg = $exception->getMessage();
	        if (preg_match("/.+Integrity constraint violation: 1062 Duplicate entry '(.+)' for key '(.+)'/is", $msg, $matches)) {
	            $msg = "导入失败，包含【{$matches[1]}】的记录已存在";
	        };
	        $this->error($msg);
	    } catch (Exception $e) {
	        $this->error($e->getMessage());
	    }

	    $this->success();
	}

	/**
	 * 绑定物流
	 */
	public function bindwl()
	{
	    $id = $this->request->param('id', 0);
	    $wl_type = $this->request->param('wl_type', -1);

	    // 调试输出
	    $debug_info = "绑定物流方法被调用，ID参数: " . $id . "\n";
	    $debug_info .= "请求参数: " . json_encode($this->request->param()) . "\n";

	    // 写入日志
	    \think\Log::write($debug_info, 'debug');

	    if (!$id) {
	        $this->error(__('Parameter %s can not be empty', 'id'));
	    }

	    $row = $this->model->get($id);
	    if (!$row) {
	        $this->error(__('No Results were found'));
	    }

	    if ($this->request->isPost()) {
	        // 从POST请求中获取id和wl_type
	        $post_id = $this->request->post('id', 0);
	        $post_wl_type = $this->request->post('wl_type', -1);

	        // 如果POST中有id，则使用POST中的id
	        if ($post_id) {
	            $id = $post_id;
	            $row = $this->model->get($id);
	            if (!$row) {
	                $this->error(__('No Results were found'));
	            }
	        }

	        // 使用POST中的wl_type
	        $params = ['wl_type' => $post_wl_type];
	        $result = $row->save($params);
	        if ($result !== false) {
	            // 记录成功日志
	           // \think\Log::write("物流绑定成功，国家ID: {$id}, 物流类型: {$post_wl_type}", 'info');
	            $this->success('物流绑定成功', '', ['id' => $id, 'wl_type' => $post_wl_type]);
	        } else {
	            // 记录失败日志
	            \think\Log::write("物流绑定失败，国家ID: {$id}, 物流类型: {$post_wl_type}", 'error');
	            $this->error(__('No rows were updated'));
	        }
	    }

	    $this->view->assign('row', $row);
	    $this->view->assign('wlTypeList', $this->model->getWlTypeList());
	    return $this->view->fetch();
	}
}
