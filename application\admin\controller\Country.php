<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use Exception;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use think\Db;
/**
 * 国家信息管理
 *
 * @icon fa fa-circle-o
 */
class Country extends Backend
{

    /**
     * Country模型对象
     * @var \app\admin\model\Country
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Country;
        $this->view->assign("wlTypeList", $this->model->getWlTypeList());
    }



    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

	public function import()
	{
	    $file = $this->request->request('file');
	    if (!$file) {
	        $this->error(__('Parameter %s can not be empty', 'file'));
	    }
	    $filePath = ROOT_PATH . DS . 'public' . DS . $file;
	    if (!is_file($filePath)) {
	        $this->error(__('No results were found'));
	    }
	    //实例化reader
	    $ext = pathinfo($filePath, PATHINFO_EXTENSION);
	    if (!in_array($ext, ['csv', 'xls', 'xlsx'])) {
	        $this->error(__('Unknown data format'));
	    }
	    if ($ext === 'csv') {
	        $file = fopen($filePath, 'r');
	        $filePath = tempnam(sys_get_temp_dir(), 'import_csv');
	        $fp = fopen($filePath, 'w');
	        $n = 0;
	        while ($line = fgets($file)) {
	            $line = rtrim($line, "\n\r\0");
	            $encoding = mb_detect_encoding($line, ['utf-8', 'gbk', 'latin1', 'big5']);
	            if ($encoding !== 'utf-8') {
	                $line = mb_convert_encoding($line, 'utf-8', $encoding);
	            }
	            if ($n == 0 || preg_match('/^".*"$/', $line)) {
	                fwrite($fp, $line . "\n");
	            } else {
	                fwrite($fp, '"' . str_replace(['"', ','], ['""', '","'], $line) . "\"\n");
	            }
	            $n++;
	        }
	        fclose($file) || fclose($fp);

	        $reader = new Csv();
	    } elseif ($ext === 'xls') {
	        $reader = new Xls();
	    } else {
	        $reader = new Xlsx();
	    }


		 $fieldArr = [
			 '国家简码' => 'sxname',
			 '中文国家名' => 'name',
			 '英文国家名' => 'ename',
			 '发货时效' => 'deliver_days',
		 ];

	    //加载文件
	    $insert = [];
	    try {
	        if (!$PHPExcel = $reader->load($filePath)) {
	            $this->error(__('Unknown data format'));
	        }
	        $currentSheet = $PHPExcel->getSheet(0);  //读取文件中的第一个工作表
	        $allColumn = $currentSheet->getHighestDataColumn(); //取得最大的列号
	        $allRow = $currentSheet->getHighestRow(); //取得一共有多少行
	        $maxColumnNumber = Coordinate::columnIndexFromString($allColumn);
	        $fields = [];
	        for ($currentRow = 1; $currentRow <= 1; $currentRow++) {
	            for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
	                $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
	                $fields[] = $val;
	            }
	        }

	        for ($currentRow = 2; $currentRow <= $allRow; $currentRow++) {
	            $values = [];
	            for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
	                $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
	                $values[] = is_null($val) ? '' : $val;
	            }
	            $row = [];
	            $temp = array_combine($fields, $values);
	            foreach ($temp as $k => $v) {
	                if (isset($fieldArr[$k]) && $k !== '') {
	                    $row[$fieldArr[$k]] = $v;
	                }
	            }
	            if ($row) {
	                $insert[] = $row;
	            }
	        }
	    } catch (Exception $exception) {
	        $this->error($exception->getMessage());
	    }
	    if (!$insert) {
	        $this->error(__('No rows were updated'));
	    }
		// print_r($insert);exit;
	    try {
	        //是否包含admin_id字段
			foreach($insert as &$row){
				$country_row = $this->model->where('name',$row['name'])->find();
				if($country_row){
					$row['id'] = $country_row['id'];
				}
			}
	        $this->model->saveAll($insert);
	    } catch (PDOException $exception) {
	        $msg = $exception->getMessage();
	        if (preg_match("/.+Integrity constraint violation: 1062 Duplicate entry '(.+)' for key '(.+)'/is", $msg, $matches)) {
	            $msg = "导入失败，包含【{$matches[1]}】的记录已存在";
	        };
	        $this->error($msg);
	    } catch (Exception $e) {
	        $this->error($e->getMessage());
	    }

	    $this->success();
	}

	/**
	 * 绑定物流
	 */
	public function bindwl()
	{
	    $id = $this->request->param('id', 0);
	    $wl_type = $this->request->param('wl_type', -1);

	    // 调试输出
	    $debug_info = "绑定物流方法被调用，ID参数: " . $id . "\n";
	    $debug_info .= "请求参数: " . json_encode($this->request->param()) . "\n";

	    // 写入日志
	    \think\Log::write($debug_info, 'debug');

	    if (!$id) {
	        $this->error(__('Parameter %s can not be empty', 'id'));
	    }

	    $row = $this->model->get($id);
	    if (!$row) {
	        $this->error(__('No Results were found'));
	    }

	    if ($this->request->isPost()) {
	        // 从POST请求中获取id和wl_type
	        $post_id = $this->request->post('id', 0);
	        $post_wl_type = $this->request->post('wl_type', -1);

	        // 如果POST中有id，则使用POST中的id
	        if ($post_id) {
	            $id = $post_id;
	            $row = $this->model->get($id);
	            if (!$row) {
	                $this->error(__('No Results were found'));
	            }
	        }

	        // 获取物流码
	        $post_wl_code = $this->request->post('wl_code', '');

	        // 使用POST中的wl_type和wl_code
	        $params = [
	            'wl_type' => $post_wl_type,
	            'wl_code' => $post_wl_code
	        ];
	        $result = $row->save($params);
	        if ($result !== false) {
	            // 记录成功日志
	            \think\Log::write("物流绑定成功，国家ID: {$id}, 物流类型: {$post_wl_type}, 物流码: {$post_wl_code}", 'info');
	            $this->success('物流绑定成功', '', ['id' => $id, 'wl_type' => $post_wl_type, 'wl_code' => $post_wl_code]);
	        } else {
	            // 记录失败日志
	            \think\Log::write("物流绑定失败，国家ID: {$id}, 物流类型: {$post_wl_type}, 物流码: {$post_wl_code}", 'error');
	            $this->error(__('No rows were updated'));
	        }
	    }

	    $this->view->assign('row', $row);
	    $this->view->assign('wlTypeList', $this->model->getWlTypeList());
	    return $this->view->fetch();
	}

	/**
	 * 获取物流码列表
	 */
	public function getLogisticsCodes()
	{
	    $wl_type = $this->request->get('wl_type', -1);
	    $country_code = $this->request->get('country_code', '');

	    \think\Log::write("获取物流码请求 - 物流类型: {$wl_type}, 国家代码: {$country_code}", 'debug');

	    if ($wl_type == -1) {
	        return json(['code' => 0, 'msg' => '请选择物流方式']);
	    }

	    try {
	        $logisticsCodes = [];

	        if ($wl_type == 0) {
	            // UBI物流 - 获取服务代码
	            \think\Log::write('开始获取UBI物流服务代码', 'debug');
	            $logisticsCodes = $this->getUbiServiceCodes();
	        } elseif ($wl_type == 3) {
	            // 云途物流 - 获取服务代码
	            \think\Log::write('开始获取云途物流服务代码', 'debug');
	            $logisticsCodes = $this->getYuntuServiceCodes($country_code);
	        } else {
	            \think\Log::write("不支持的物流类型: {$wl_type}", 'error');
	            return json(['code' => 0, 'msg' => '不支持的物流类型']);
	        }

	        \think\Log::write('最终返回的物流码数量: ' . count($logisticsCodes), 'debug');

	        if (empty($logisticsCodes)) {
	            return json(['code' => 0, 'msg' => '暂无可用的物流码']);
	        }

	        return json(['code' => 1, 'msg' => '获取成功', 'data' => $logisticsCodes]);

	    } catch (\Exception $e) {
	        \think\Log::write('获取物流码失败: ' . $e->getMessage() . ' 堆栈: ' . $e->getTraceAsString(), 'error');
	        return json(['code' => 0, 'msg' => '获取物流码失败：' . $e->getMessage()]);
	    }
	}

	/**
	 * 获取UBI物流服务代码
	 */
	private function getUbiServiceCodes()
	{
	    try {
	        $ubi = new \wuliu\Ubi();
	        $serviceResult = $ubi->getServicesCateLog();

	        \think\Log::write('UBI服务代码API返回: ' . $serviceResult, 'debug');

	        $serviceData = json_decode($serviceResult, true);

	        $serviceCodes = [];

	        // 检查不同的数据结构（参考Order控制器的实现）
	        if (isset($serviceData['data']) && is_array($serviceData['data'])) {
	            // 如果data是数组，直接遍历
	            foreach ($serviceData['data'] as $service) {
	                if (isset($service['serviceCode']) && isset($service['serviceName'])) {
	                    $serviceCodes[] = [
	                        'code' => $service['serviceCode'],
	                        'name' => $service['serviceName'],
	                        'nativeName' => isset($service['nativeName']) ? $service['nativeName'] : $service['serviceName']
	                    ];
	                }
	            }
	        } elseif (isset($serviceData['data']) && is_string($serviceData['data'])) {
	            // 如果data是字符串，再次解析
	            $innerData = json_decode($serviceData['data'], true);
	            if (isset($innerData['data']) && is_array($innerData['data'])) {
	                foreach ($innerData['data'] as $service) {
	                    if (isset($service['serviceCode']) && isset($service['serviceName'])) {
	                        $serviceCodes[] = [
	                            'code' => $service['serviceCode'],
	                            'name' => $service['serviceName'],
	                            'nativeName' => isset($service['nativeName']) ? $service['nativeName'] : $service['serviceName']
	                        ];
	                    }
	                }
	            }
	        }

	        // 如果没有获取到服务代码，使用默认值（参考Order控制器）
	        if (empty($serviceCodes)) {
	            \think\Log::write('UBI API未返回数据，使用默认服务代码', 'info');
	            $serviceCodes = [];
	        }

	        \think\Log::write('UBI服务代码解析结果: ' . json_encode($serviceCodes), 'debug');
	        return $serviceCodes;

	    } catch (\Exception $e) {
	        \think\Log::write('获取UBI物流服务代码失败: ' . $e->getMessage(), 'error');
	        // 如果API调用失败，返回空数组（参考Order控制器）
	        return [];
	    }
	}

	/**
	 * 获取云途物流服务代码
	 */
	private function getYuntuServiceCodes($countryCode)
	{
	    try {
	        $yuntu = new \wuliu\Yuntu();
	        // 可以根据需要传递国家代码，这里先不传递获取所有服务
	        $result = $yuntu->GetShippingMethods($countryCode);

	        \think\Log::write('云途服务代码API返回: ' . $result, 'debug');

	        $serviceData = json_decode($result, true);
	        $serviceCodes = [];

	        // 检查云途物流API返回的数据结构（参考Order控制器）
	        if (isset($serviceData['Code']) && $serviceData['Code'] === '0000' && isset($serviceData['Items'])) {
	            foreach ($serviceData['Items'] as $service) {
	                if (isset($service['Code']) && isset($service['EName'])) {
	                    $serviceCodes[] = [
	                        'code' => $service['Code'],
	                        'name' => $service['EName'],
	                        'nativeName' => isset($service['CName']) ? $service['CName'] : $service['EName']
	                    ];
	                }
	            }
	        }

	        // 如果没有获取到服务代码，使用默认值（参考Order控制器）
	        if (empty($serviceCodes)) {
	            \think\Log::write('云途API未返回数据，使用默认服务代码', 'info');
	            $serviceCodes = [
	                ['code' => 'YT001', 'name' => 'Yuntu Standard', 'nativeName' => '云途标准服务'],
	                ['code' => 'YT002', 'name' => 'Yuntu Express', 'nativeName' => '云途快速服务']
	            ];
	        }

	        \think\Log::write('云途服务代码解析结果: ' . json_encode($serviceCodes), 'debug');
	        return $serviceCodes;
	    } catch (\Exception $e) {
	        \think\Log::write('获取云途物流服务代码失败: ' . $e->getMessage(), 'error');
	        // 如果API调用失败，返回默认服务代码（参考Order控制器）
	        return [
	            ['code' => 'YT001', 'name' => 'Yuntu Standard', 'nativeName' => '云途标准服务'],
	            ['code' => 'YT002', 'name' => 'Yuntu Express', 'nativeName' => '云途快速服务']
	        ];
	    }
	}

	/**
	 * 测试物流类加载
	 */
	public function testLogistics()
	{
	    try {
	        // 测试UBI物流类
	        $ubi = new \wuliu\Ubi();
	        $ubiResult = "UBI类实例化成功";

	        // 测试云途物流类
	        $yuntu = new \wuliu\Yuntu();
	        $yuntuResult = "云途类实例化成功";

	        return json([
	            'code' => 1,
	            'msg' => '物流类测试成功',
	            'data' => [
	                'ubi' => $ubiResult,
	                'yuntu' => $yuntuResult
	            ]
	        ]);

	    } catch (\Exception $e) {
	        return json([
	            'code' => 0,
	            'msg' => '物流类测试失败：' . $e->getMessage(),
	            'trace' => $e->getTraceAsString()
	        ]);
	    }
	}
}
