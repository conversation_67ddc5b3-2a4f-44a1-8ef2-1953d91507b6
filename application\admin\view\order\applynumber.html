<style>
.form-group {
    margin-bottom: 15px;
}
.control-label {
    font-weight: bold;
}
.form-control[readonly] {
    background-color: #f5f5f5;
}
.required-field {
    color: #d9534f;
}
.has-error .form-control {
    border-color: #d9534f;
    box-shadow: inset 0 1px 1px rgba(0,0,0,.075), 0 0 6px #ce8483;
}
.has-error .control-label {
    color: #d9534f;
}

/* 错误信息弹窗样式 */
.error-dialog {
    max-height: 400px;
    overflow-y: auto;
}
.error-dialog .error-title {
    color: #d9534f;
    font-weight: bold;
    margin-bottom: 15px;
    font-size: 16px;
}
.error-dialog .error-content {
    background-color: #f8f8f8;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
    font-size: 14px;
    line-height: 1.5;
    word-wrap: break-word;
    color: #333;
}
.error-dialog .error-details {
    color: #666;
    font-size: 12px;
    margin-top: 10px;
}
.error-dialog .error-code {
    color: #d9534f;
    font-weight: bold;
}

</style>

<form id="apply-form" class="form-horizontal" role="form" onsubmit="return false;">
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">订单编号:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" value="{$row.ddbh}" readonly>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2"><span class="required-field">*</span>服务代码:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-service_code" name="row[service_code]" class="form-control selectpicker" data-rule="required">
                <option value="">请选择服务代码</option>
                {foreach name="serviceCodes" item="service"}
                <option value="{$service.code}">{$service.name} - {$service.nativeName}</option>
                {/foreach}
            </select>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2"><span class="required-field">*</span>英文品名:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-english_name" name="row[english_name]" type="text" value="{$row['names']}" class="form-control" data-rule="required" placeholder="请输入英文品名">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2"><span class="required-field">*</span>中文品名:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-china_name" name="row[name]" type="text" class="form-control" data-rule="required" placeholder="请输入中文品名">
        </div>
    </div>

    <!-- 云途物流特有字段 -->
    {if $wl_type == 3}
    <div class="form-group yuntu-field">
        <label class="control-label col-xs-12 col-sm-2"><span class="required-field">*</span>包裹数量:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-package_count" name="row[package_count]" type="number" min="1" value="1" class="form-control" data-rule="required" placeholder="请输入包裹数量">
        </div>
    </div>
    {/if}

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2"><span class="required-field">*</span>重量:</label>
        <div class="col-xs-12 col-sm-6">
            <input id="c-weight" name="row[weight]" type="number" step="0.01" class="form-control" data-rule="required" placeholder="请输入重量">
        </div>
        <div class="col-xs-12 col-sm-2">
            <select id="c-weight_unit" name="row[weight_unit]" class="form-control" data-rule="required">
                <option value="KG" selected>kg</option>
                <option value="G">g</option>
            </select>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2"><span class="required-field">*</span>货值:</label>
        <div class="col-xs-12 col-sm-6">
            <input id="c-value" name="row[value]" type="number" step="0.01" class="form-control" data-rule="required" placeholder="请输入货值">
        </div>
        <div class="col-xs-12 col-sm-2">
            <select id="c-currency" name="row[currency]" class="form-control" data-rule="required">
                <option value="USD" selected>USD</option>
                <option value="EUR">EUR</option>
                <option value="GBP">GBP</option>
                <option value="CNY">CNY</option>
            </select>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2"><span class="required-field">*</span>收件人姓名:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-recipient_name" name="row[recipient_name]" type="text" class="form-control" value="{$row.khname}" placeholder="收件人姓名">
        </div>
    </div>

    <!-- 云途物流特有字段：收件人电话 -->
    {if $wl_type == 3}
    <div class="form-group yuntu-field">
        <label class="control-label col-xs-12 col-sm-2">收件人电话:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-recipient_phone" name="row[recipient_phone]" type="text" class="form-control" value="{$row.lxfs|default=''}" placeholder="收件人电话">
        </div>
    </div>
    {/if}

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2"><span class="required-field">*</span>地址第一行:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-address_line1" name="row[address_line1]" type="text" class="form-control" data-rule="required" value="{$row.khaddr1}" placeholder="地址第一行（必填）">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">地址第二行:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-address_line2" name="row[address_line2]" type="text" class="form-control" value="{$row.khaddr2}" placeholder="地址第二行">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">地址第三行:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-address_line3" name="row[address_line3]" type="text" class="form-control" placeholder="地址第三行">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2"><span class="required-field">*</span>城市:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-city" name="row[city]" data-rule="required" type="text" class="form-control" value="{$row.city}" placeholder="城市">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2"><span class="required-field">*</span>省份:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-state" name="row[state]" data-rule="required" type="text" class="form-control" value="{$row.state}" placeholder="省份">
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2"><span class="required-field">*</span>国家二字代码:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-country_code" name="row[country_code]" data-rule="required" type="text" class="form-control" value="{$row.country_code}" placeholder="国家二字代码" maxlength="2">
        </div>
    </div>

    <!-- 云途物流特有字段：收件人邮编 -->
    {if $wl_type == 3}
    <div class="form-group yuntu-field">
        <label class="control-label col-xs-12 col-sm-2">收件人邮编:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-zip" name="row[zip]" type="text" class="form-control" value="{$row.zipcode|default=''}" placeholder="收件人邮编">
        </div>
    </div>
    {/if}

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2"><span class="required-field">*</span>发件人名称:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sender_name" name="row[sender_name]" type="text" class="form-control" data-rule="required" placeholder="发件人名称">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2"><span class="required-field">*</span>发件人邮箱:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sender_email" name="row[sender_email]" type="text" class="form-control" data-rule="required" placeholder="发件人邮箱">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2"><span class="required-field">*</span>发件人邮编:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sender_postcode" name="row[sender_postcode]" type="text" class="form-control" data-rule="required" placeholder="发件人邮编">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2"><span class="required-field">*</span>发件人国家二字代码:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sender_countryl" name="row[sender_country]" value="CN" type="text" class="form-control" data-rule="required" placeholder="发件人名称">
        </div>
    </div>
    <!-- UBI物流特有字段：发件人税号 -->
    {if $wl_type == 0}
    <div class="form-group ubi-field">
        <label class="control-label col-xs-12 col-sm-2"><span class="required-field">*</span>发件人税号:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sender_vendorid" name="row[sender_vendorid]" value="" type="text" class="form-control" data-rule="required" placeholder="发件人税号">
        </div>
    </div>
    {/if}

    <!-- 云途物流特有字段：发件人详细信息 -->
    {if $wl_type == 3}
    <div class="form-group yuntu-field">
        <label class="control-label col-xs-12 col-sm-2">发件人地址:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sender_address" name="row[sender_address]" type="text" class="form-control" placeholder="发件人详细地址">
        </div>
    </div>
    <div class="form-group yuntu-field">
        <label class="control-label col-xs-12 col-sm-2">发件人城市:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sender_city" name="row[sender_city]" type="text" class="form-control" placeholder="发件人城市">
        </div>
    </div>
    <div class="form-group yuntu-field">
        <label class="control-label col-xs-12 col-sm-2">发件人省/州:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sender_state" name="row[sender_state]" type="text" class="form-control" placeholder="发件人省/州">
        </div>
    </div>
    <div class="form-group yuntu-field">
        <label class="control-label col-xs-12 col-sm-2">发件人电话:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-sender_phone" name="row[sender_phone]" type="text" class="form-control" placeholder="发件人电话">
        </div>
    </div>
    {/if}

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="button" class="btn btn-primary btn-embossed" id="submit-btn">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>

<script>
// 立即阻止表单默认提交行为
(function() {
    console.log('=== 立即执行表单拦截 ===');

    // 等待DOM加载后立即绑定事件
    function bindFormEvents() {
        var form = document.getElementById('apply-form');
        var btn = document.getElementById('submit-btn');

        if (form) {
            console.log('找到表单，绑定submit事件拦截');
            form.addEventListener('submit', function(e) {
                console.log('=== 原生表单提交事件被拦截 ===');
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();
                return false;
            }, true); // 使用捕获阶段，优先级更高
        }

        // 不拦截按钮点击事件，让jQuery处理
    }

    // 如果DOM已经加载，立即执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', bindFormEvents);
    } else {
        bindFormEvents();
    }
})();

// 立即启用按钮
setTimeout(function() {
    var btn = document.getElementById('submit-btn');
    if (btn) {
        btn.disabled = false;
        btn.style.pointerEvents = 'auto';
        btn.style.opacity = '1';
        console.log('按钮已启用');
    }
}, 100);

// 全局错误拦截器 - 防止错误信息直接显示在页面上
window.addEventListener('error', function(e) {
    console.log('全局错误拦截:', e);
    e.preventDefault();
    return false;
});

// 拦截未处理的Promise错误
window.addEventListener('unhandledrejection', function(e) {
    console.log('未处理的Promise错误:', e);
    e.preventDefault();
    return false;
});

// 等待页面加载完成
document.addEventListener('DOMContentLoaded', function() {
    // 检查jQuery是否可用
    function waitForJQuery(callback) {
        if (typeof $ !== 'undefined' && typeof $.fn !== 'undefined') {
            callback();
        } else if (typeof parent !== 'undefined' && typeof parent.$ !== 'undefined') {
            // 使用父窗口的jQuery
            window.$ = parent.$;
            callback();
        } else {
            // 等待jQuery加载
            setTimeout(function() {
                waitForJQuery(callback);
            }, 100);
        }
    }

    waitForJQuery(function() {
        console.log('jQuery已加载，开始初始化表单');
        console.log('当前jQuery对象:', $);
        console.log('document.readyState:', document.readyState);
        console.log('DOM中的按钮元素:', document.getElementById('submit-btn'));

        // 设置AJAX全局错误处理器
        $(document).ajaxError(function(event, xhr, settings, thrownError) {
            console.log('AJAX全局错误拦截:', {
                event: event,
                xhr: xhr,
                settings: settings,
                thrownError: thrownError
            });

            // 阻止默认的错误处理
            event.preventDefault();
            return false;
        });

        // 监控页面内容变化，拦截可能直接显示的错误信息
        var observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === Node.TEXT_NODE || node.nodeType === Node.ELEMENT_NODE) {
                            var content = node.textContent || node.innerText || '';
                            // 更严格的检测条件，只处理真正的错误信息
                            var errorPattern = /\{"code"\s*:\s*0[^}]*"msg"\s*:\s*"[^"]*"[^}]*\}/;
                            if (errorPattern.test(content)) {
                                console.log('检测到页面错误信息:', content);
                                // 移除错误显示
                                if (node.parentNode) {
                                    node.parentNode.removeChild(node);
                                }
                                // 使用弹窗显示
                                showFormattedError(content, '系统错误');
                            }
                        }
                    });
                }
            });
        });

        // 开始监控页面变化
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // 检查页面是否已经存在错误信息并清除
        function checkAndClearExistingErrors() {
            var bodyText = document.body.textContent || document.body.innerText || '';

            // 更严格的检测条件，避免误判
            var errorPattern = /\{"code"\s*:\s*0[^}]*"msg"\s*:\s*"[^"]*"[^}]*\}/;
            if (errorPattern.test(bodyText)) {
                console.log('检测到页面已存在错误信息');

                // 查找包含错误信息的元素
                var walker = document.createTreeWalker(
                    document.body,
                    NodeFilter.SHOW_TEXT,
                    function(node) {
                        var content = node.textContent || '';
                        // 只处理真正的错误信息（code为0且包含msg的JSON）
                        if (errorPattern.test(content)) {
                            return NodeFilter.FILTER_ACCEPT;
                        }
                        return NodeFilter.FILTER_SKIP;
                    }
                );

                var errorNodes = [];
                var node;
                while (node = walker.nextNode()) {
                    errorNodes.push(node);
                }

                // 移除错误节点并显示在弹窗中
                if (errorNodes.length > 0) {
                    errorNodes.forEach(function(errorNode) {
                        var errorContent = errorNode.textContent;
                        console.log('移除错误节点:', errorContent);

                        // 移除错误显示
                        if (errorNode.parentNode) {
                            errorNode.parentNode.removeChild(errorNode);
                        }

                        // 使用弹窗显示
                        showFormattedError(errorContent, '系统错误');
                    });
                }
            }
        }

        // 页面加载完成后延迟检查，避免过早触发
        // 注释掉自动检查，只在实际出现错误时才处理
        // setTimeout(checkAndClearExistingErrors, 500);

        // 格式化错误信息弹窗函数
        function showFormattedError(errorData, title) {
            console.log('=== showFormattedError 开始 ===');
            console.log('errorData:', errorData);
            console.log('title:', title);

            // 先清理可能存在的旧错误弹窗（不关闭父弹窗）
            if (parent && parent.Layer) {
                try {
                    // 只关闭错误弹窗，不关闭所有弹窗
                    if (window.currentErrorDialogIndex) {
                        parent.Layer.close(window.currentErrorDialogIndex);
                        console.log('已关闭现有错误弹窗:', window.currentErrorDialogIndex);
                        window.currentErrorDialogIndex = null;
                    }
                } catch (e) {
                    console.log('清理错误弹窗时出错:', e);
                }
            }

            // 清理原生弹窗
            try {
                if (window.closeNativeErrorDialog) {
                    window.closeNativeErrorDialog();
                }
            } catch (e) {
                console.log('清理原生弹窗时出错:', e);
            }

            title = title || '错误信息';

            // 延迟创建新弹窗，确保清理完成
            setTimeout(function() {
                createErrorDialog(errorData, title);
            }, 100);
        }

        // 创建错误弹窗的实际函数
        function createErrorDialog(errorData, title) {
            console.log('=== createErrorDialog 开始 ===');

            var errorMsg = '';
            var errorCode = '';

            // 处理不同类型的错误数据，提取msg信息
            if (typeof errorData === 'string') {
                // 检查是否包含JSON格式的错误信息
                if (errorData.indexOf('{"code"') !== -1 || errorData.indexOf('{"msg"') !== -1) {
                    try {
                        // 尝试解析JSON
                        var parsed = JSON.parse(errorData);
                        errorMsg = parsed.msg || parsed.message || errorData;
                        errorCode = parsed.code !== undefined ? parsed.code : '';
                    } catch (e) {
                        // JSON解析失败，但可能包含JSON片段，尝试提取
                        var jsonMatch = errorData.match(/\{"code"[^}]+\}/);
                        if (jsonMatch) {
                            try {
                                var jsonPart = JSON.parse(jsonMatch[0]);
                                errorMsg = jsonPart.msg || jsonPart.message || errorData;
                                errorCode = jsonPart.code !== undefined ? jsonPart.code : '';
                            } catch (e2) {
                                errorMsg = errorData;
                            }
                        } else {
                            errorMsg = errorData;
                        }
                    }
                } else {
                    errorMsg = errorData;
                }
            } else if (typeof errorData === 'object' && errorData !== null) {
                errorMsg = errorData.msg || errorData.message || errorData.statusText || '未知错误';
                errorCode = errorData.code !== undefined ? errorData.code : (errorData.status || '');

                // 如果是AJAX错误对象，尝试从parsedResponse中获取信息
                if (errorData.parsedResponse) {
                    errorMsg = errorData.parsedResponse.msg || errorData.parsedResponse.message || errorMsg;
                    errorCode = errorData.parsedResponse.code !== undefined ? errorData.parsedResponse.code : errorCode;
                } else if (errorData.rawResponse) {
                    // 尝试从原始响应中解析错误信息
                    if (typeof errorData.rawResponse === 'string' && errorData.rawResponse.indexOf('{"code"') !== -1) {
                        try {
                            var rawParsed = JSON.parse(errorData.rawResponse);
                            errorMsg = rawParsed.msg || rawParsed.message || errorMsg;
                            errorCode = rawParsed.code !== undefined ? rawParsed.code : errorCode;
                        } catch (e) {
                            errorMsg = errorData.rawResponse;
                        }
                    } else {
                        errorMsg = errorData.rawResponse;
                    }
                }
            } else {
                errorMsg = String(errorData) || '未知错误';
            }

            var dialogContent = '<div class="error-dialog">' +
                '<div class="error-content">' + errorMsg + '</div>';

            if (errorCode) {
                dialogContent += '<div class="error-details">错误代码: <span class="error-code">' + errorCode + '</span></div>';
            }

            dialogContent += '</div>';

            console.log('最终错误消息:', errorMsg);
            console.log('错误代码:', errorCode);
            console.log('弹窗内容:', dialogContent);

            // 使用Layer弹窗显示
            if (parent && parent.Layer) {
                console.log('使用Layer弹窗显示错误');
                console.log('parent.Layer对象:', parent.Layer);

                var errorIndex = parent.Layer.open({
                    type: 1,
                    title: '错误信息',
                    area: ['60%', '40%'],
                    content: dialogContent,
                    btn: ['关闭'],
                    btnAlign: 'c',
                    zIndex: parent.Layer.zIndex, // 使用Layer自动管理的层级
                    shade: 0.3, // 添加遮罩
                    shadeClose: false, // 禁用点击遮罩关闭
                    closeBtn: 1, // 显示关闭按钮
                    maxmin: false,
                    resize: false,
                    move: '.layui-layer-title', // 只能通过标题拖拽
                    success: function(layero, index) {
                        console.log('Layer弹窗创建成功，layero:', layero, 'index:', index);
                        console.log('弹窗元素可见性:', layero.is(':visible'));
                        console.log('弹窗位置:', layero.offset());
                        console.log('弹窗尺寸:', layero.width(), 'x', layero.height());
                    },
                    end: function() {
                        console.log('Layer弹窗已关闭');
                        // 清理相关变量，确保下次能正常创建
                        window.currentErrorContent = null;
                        window.currentErrorDialogIndex = null;
                    }
                });
                console.log('Layer弹窗索引:', errorIndex);

                // 存储错误弹窗索引和内容
                window.currentErrorDialogIndex = errorIndex;
                window.currentErrorContent = errorMsg;

                // 额外检查：如果弹窗没有正确显示，尝试强制显示
                setTimeout(function() {
                    var layerElement = parent.$('#layui-layer' + errorIndex);
                    if (layerElement.length > 0) {
                        console.log('找到Layer元素:', layerElement);
                        console.log('Layer元素样式:', layerElement.attr('style'));
                        // 强制设置可见
                        layerElement.show().css({
                            'z-index': 99999999,
                            'display': 'block',
                            'visibility': 'visible'
                        });
                    } else {
                        console.error('未找到Layer元素 #layui-layer' + errorIndex);
                        console.log('Layer弹窗可能创建失败，使用备用方案');
                        // 备用方案：使用原生弹窗
                        showNativeErrorDialog(errorMsg, title);
                    }
                }, 100);

            } else {
                console.log('Layer不可用，使用alert显示错误');
                // 降级处理：使用alert
                alert(title + ':\n' + errorMsg);
            }
            console.log('=== createErrorDialog 结束 ===');
        }

        // 原生弹窗备用方案
        function showNativeErrorDialog(errorMsg, title) {
            console.log('=== 使用原生弹窗显示错误 ===');

            // 创建弹窗HTML
            var dialogHtml = '<div id="native-error-dialog" style="' +
                'position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); ' +
                'width: 60%; max-width: 600px; min-width: 400px; ' +
                'background: white; border: 1px solid #ddd; border-radius: 6px; ' +
                'box-shadow: 0 4px 20px rgba(0,0,0,0.3); z-index: 99999999; ' +
                'font-family: Arial, sans-serif;' +
                '">' +
                '<div style="padding: 15px; border-bottom: 1px solid #eee; background: #f8f9fa; border-radius: 6px 6px 0 0;">' +
                '<h4 style="margin: 0; color: #d9534f; font-size: 16px;">' + title + '</h4>' +
                '</div>' +
                '<div style="padding: 20px; max-height: 300px; overflow-y: auto;">' +
                '<div style="background: #f8f8f8; border: 1px solid #ddd; border-radius: 4px; padding: 15px; margin-bottom: 15px; font-size: 14px; line-height: 1.5; word-wrap: break-word; color: #333;">' +
                errorMsg +
                '</div>' +
                '</div>' +
                '<div style="padding: 15px; text-align: center; border-top: 1px solid #eee; background: #f8f9fa; border-radius: 0 0 6px 6px;">' +
                '<button onclick="closeNativeErrorDialog()" style="padding: 8px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">关闭</button>' +
                '</div>' +
                '</div>';

            // 创建遮罩（不可点击关闭）
            var overlay = '<div id="native-error-overlay" style="' +
                'position: fixed; top: 0; left: 0; width: 100%; height: 100%; ' +
                'background: rgba(0,0,0,0.3); z-index: 99999998;' +
                '"></div>';

            // 添加到页面
            if (parent && parent.document) {
                parent.document.body.insertAdjacentHTML('beforeend', overlay + dialogHtml);
            } else {
                document.body.insertAdjacentHTML('beforeend', overlay + dialogHtml);
            }

            // 存储错误内容
            window.nativeErrorContent = errorMsg;

            console.log('原生弹窗已创建');
        }

        // 关闭原生弹窗
        window.closeNativeErrorDialog = function() {
            var dialog = (parent && parent.document) ? parent.document.getElementById('native-error-dialog') : document.getElementById('native-error-dialog');
            var overlay = (parent && parent.document) ? parent.document.getElementById('native-error-overlay') : document.getElementById('native-error-overlay');

            if (dialog) dialog.remove();
            if (overlay) overlay.remove();
            console.log('原生弹窗已关闭');
        };

        // 复制原生弹窗内容
        window.copyNativeErrorContent = function() {
            if (window.nativeErrorContent) {
                var textArea = document.createElement('textarea');
                textArea.value = window.nativeErrorContent;
                document.body.appendChild(textArea);
                textArea.select();

                try {
                    document.execCommand('copy');
                    alert('错误信息已复制到剪贴板');
                } catch (err) {
                    alert('复制失败，请手动复制');
                }

                document.body.removeChild(textArea);
            }
        };

        // 复制错误内容函数
        window.copyErrorContent = function() {
            if (window.currentErrorContent) {
                // 创建临时文本区域
                var textArea = document.createElement('textarea');
                textArea.value = window.currentErrorContent;
                document.body.appendChild(textArea);
                textArea.select();

                try {
                    document.execCommand('copy');
                    if (parent && parent.Layer) {
                        parent.Layer.msg('错误信息已复制到剪贴板', {icon: 1});
                    } else {
                        alert('错误信息已复制到剪贴板');
                    }
                } catch (err) {
                    if (parent && parent.Layer) {
                        parent.Layer.msg('复制失败，请手动复制', {icon: 2});
                    } else {
                        alert('复制失败，请手动复制');
                    }
                }

                document.body.removeChild(textArea);
            }
        };

        // 延迟处理按钮，确保DOM完全加载
        setTimeout(function() {
            console.log('=== 延迟处理按钮开始 ===');

            // 确保按钮可点击
            var $btn = $("#submit-btn");
            console.log('jQuery版本:', $.fn ? $.fn.jquery : 'jQuery未加载');
            console.log('找到的按钮元素:', $btn);
            console.log('按钮元素数量:', $btn.length);

            if ($btn.length > 0) {
                $btn.prop('disabled', false).removeClass('disabled');
                console.log('按钮状态已更新，disabled:', $btn.prop('disabled'));
                initializeButton($btn);
            } else {
                console.error('未找到按钮元素 #submit-btn');
                // 尝试用原生方法查找
                var nativeBtn = document.getElementById('submit-btn');
                console.log('原生方法找到的按钮:', nativeBtn);
                if (nativeBtn) {
                    nativeBtn.disabled = false;
                    $btn = $(nativeBtn);
                    console.log('使用原生元素重新创建jQuery对象，长度:', $btn.length);
                    if ($btn.length > 0) {
                        initializeButton($btn);
                    }
                }
            }
        }, 200);

        // 按钮初始化函数
        function initializeButton($btn) {
            console.log('=== 初始化按钮 ===');

            // 强制移除按钮的type属性，防止默认表单提交
            $btn.attr('type', 'button');

            // 移除所有现有的事件监听器
            $btn.off('click');

            // 按钮点击事件 - 直接处理AJAX提交
            $btn.on('click', function(e) {
                console.log('=== jQuery按钮点击事件 ===');
                e.preventDefault();
                e.stopPropagation();

                // 直接调用提交处理函数
                handleFormSubmit();
            });
        }

        // 提取表单提交处理逻辑到独立函数
        function handleFormSubmit() {
            console.log('=== handleFormSubmit 开始 ===');

            var $form = $("#apply-form");
            var $btn = $("#submit-btn");

            // 简单验证必填字段
            var isValid = true;
            // 根据物流类型动态设置必填字段
            var requiredFields = ['service_code', 'english_name', 'weight', 'value', 'address_line1', 'sender_name', 'sender_email', 'sender_postcode', 'sender_country'];

            // 获取物流类型
            var wlType = parseInt('{$wl_type|default=0}');

            // 根据物流类型添加特定的必填字段
            if (wlType == 0) {
                // UBI物流需要发件人税号
                requiredFields.push('sender_vendorid');
            } else if (wlType == 3) {
                // 云途物流需要包裹数量和中文品名
                requiredFields.push('package_count', 'name');
            }

            $.each(requiredFields, function(index, field) {
                var $field = $form.find('[name="row[' + field + ']"]');
                if (!$field.val() || $field.val().trim() === '') {
                    isValid = false;
                    $field.closest('.form-group').addClass('has-error');
                } else {
                    $field.closest('.form-group').removeClass('has-error');
                }
            });

            if (!isValid) {
                if (parent && parent.Layer) {
                    parent.Layer.msg('请填写所有必填字段', {icon: 2});
                } else {
                    alert('请填写所有必填字段');
                }
                return false;
            }

            // 显示加载状态
            $btn.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 申请中...');

            // 提交表单
            $.ajax({
                url: window.location.href,
                type: 'POST',
                data: $form.serialize(),
                dataType: 'text', // 改为text，手动解析JSON，避免自动解析错误
                success: function(response, textStatus, xhr) {
                    console.log('=== AJAX Success 回调开始 ===');
                    console.log('服务器返回原始数据:', response);
                    console.log('textStatus:', textStatus);
                    console.log('xhr.status:', xhr.status);

                    // 恢复按钮状态
                    $btn.prop('disabled', false).html('确定');

                    var data = null;
                    try {
                        // 手动解析JSON
                        data = JSON.parse(response);
                        console.log('解析后的数据:', data);
                        console.log('data.code:', data.code);
                        console.log('data.msg:', data.msg);
                    } catch (e) {
                        console.log('JSON解析失败，错误:', e);
                        console.log('原始响应:', response);
                        // 如果不是JSON格式，直接显示错误
                        console.log('调用 showFormattedError - 响应格式错误');
                        showFormattedError(response, '服务器响应格式错误');
                        return;
                    }

                    if (data && data.code == 1) {
                        console.log('=== 成功分支 ===');
                        // 成功
                        var successMsg = data.msg || '申请单号成功';
                        if (data.data && data.data.tracking_number) {
                            successMsg += '，单号：' + data.data.tracking_number;
                        }

                        if (parent && parent.Layer) {
                            parent.Layer.msg(successMsg, {icon: 1, time: 2000});
                            // 刷新父页面并关闭窗口
                            setTimeout(function() {
                                if (parent.$(".btn-refresh").length > 0) {
                                    parent.$(".btn-refresh").trigger("click");
                                }
                                // 关闭当前窗口
                                var index = parent.Layer.getFrameIndex(window.name);
                                parent.Layer.close(index);
                            }, 1500);
                        } else {
                            alert(successMsg);
                            window.close();
                        }
                    } else {
                        console.log('=== 失败分支 ===');
                        console.log('调用 showFormattedError - 申请单号失败');
                        // 失败 - 使用格式化弹窗显示详细错误信息
                        showFormattedError(data, '申请单号失败');
                    }
                    console.log('=== AJAX Success 回调结束 ===');
                },
                error: function(xhr, status, error) {
                    console.log('=== AJAX Error 回调开始 ===');
                    console.log('xhr:', xhr);
                    console.log('status:', status);
                    console.log('error:', error);
                    console.log('xhr.status:', xhr.status);
                    console.log('xhr.statusText:', xhr.statusText);
                    console.log('xhr.responseText:', xhr.responseText);

                    // 恢复按钮状态
                    $btn.prop('disabled', false).html('确定');

                    // 构建详细的错误信息对象
                    var errorData = {
                        status: xhr.status,
                        statusText: xhr.statusText,
                        error: error,
                        responseText: xhr.responseText
                    };

                    // 尝试解析服务器返回的错误信息
                    if (xhr.responseText) {
                        try {
                            var response = JSON.parse(xhr.responseText);
                            console.log('解析的错误响应:', response);
                            errorData.parsedResponse = response;
                        } catch (e) {
                            console.log('错误响应JSON解析失败:', e);
                            // 如果不是JSON格式，保持原始文本
                            errorData.rawResponse = xhr.responseText;
                        }
                    }

                    console.log('调用 showFormattedError - AJAX请求失败');
                    // 使用格式化弹窗显示详细错误信息
                    showFormattedError(errorData, 'AJAX请求失败');
                    console.log('=== AJAX Error 回调结束 ===');
                }
            });
        }

        // 绑定表单提交事件 - 作为备用
        $("#apply-form").on('submit', function(e) {
            console.log('=== 表单提交事件被触发 ===');
            e.preventDefault();
            e.stopPropagation();
            e.stopImmediatePropagation();

            // 调用处理函数
            handleFormSubmit();
            return false;
        });

        // 绑定重置按钮
        $("#apply-form button[type='reset']").on('click', function() {
            $("#apply-form .form-group").removeClass('has-error');
        });
    });

    // 备用：如果jQuery加载失败，使用原生JavaScript
    setTimeout(function() {
        var submitBtn = document.getElementById('submit-btn');
        if (submitBtn && submitBtn.disabled) {
            submitBtn.disabled = false;
            submitBtn.onclick = function(e) {
                e.preventDefault();
                alert('请等待页面完全加载后再试，或刷新页面重试');
                return false;
            };
        }
    }, 3000);
});
</script>
