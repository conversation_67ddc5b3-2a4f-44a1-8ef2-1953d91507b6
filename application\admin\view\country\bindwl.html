<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <div class="panel-lead"><em>绑定物流</em>为国家 <b>{$row.name}</b> 绑定物流方式</div>
    </div>
    <div class="panel-body">
        <form id="bindwl-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="" novalidate>
            {:token()}
            <input type="hidden" name="id" value="{$row.id}" />

            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-2">{:__('国家')}:</label>
                <div class="col-xs-12 col-sm-8">
                    <input type="text" class="form-control" value="{$row.name}" readonly />
                </div>
            </div>
            <div class="form-group">
                <label class="control-label col-xs-12 col-sm-2">{:__('物流方式')}:</label>
                <div class="col-xs-12 col-sm-8">
                    <select id="c-wl_type" class="form-control selectpicker" name="wl_type">
                        <option value="-1">请选择物流方式</option>
                        {foreach name="wlTypeList" item="vo" key="k"}
                        <option value="{$k}" {in name="k" value="$row.wl_type"}selected{/in}>{$vo}</option>
                        {/foreach}
                    </select>
                </div>
            </div>
            <div class="form-group" id="wl-code-group" style="display: none;">
                <label class="control-label col-xs-12 col-sm-2">{:__('物流码')}:</label>
                <div class="col-xs-12 col-sm-8">
                    <select id="c-wl_code" class="form-control selectpicker" name="wl_code">
                        <option value="">请先选择物流方式</option>
                    </select>
                    <div class="help-block">
                        <small class="text-muted">选择物流方式后将自动加载对应的物流码列表</small>
                    </div>
                </div>
            </div>
            <div class="form-group layer-footer">
                <label class="control-label col-xs-12 col-sm-2"></label>
                <div class="col-xs-12 col-sm-8">
                    <button type="submit" class="btn btn-success btn-embossed">{:__('OK')}</button>
                    <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
                </div>
            </div>
        </form>
    </div>
</div>
<script type="text/javascript">
    // 等待DOM加载完成
    document.addEventListener('DOMContentLoaded', function() {
        // 获取表单元素
        var form = document.getElementById('bindwl-form');
        var wlTypeSelect = document.getElementById('c-wl_type');
        var wlCodeSelect = document.getElementById('c-wl_code');
        var wlCodeGroup = document.getElementById('wl-code-group');

        // 绑定物流方式选择事件
        if (wlTypeSelect) {
            wlTypeSelect.addEventListener('change', function() {
                var selectedType = this.value;

                if (selectedType === '-1') {
                    // 隐藏物流码选择
                    wlCodeGroup.style.display = 'none';
                    wlCodeSelect.innerHTML = '<option value="">请先选择物流方式</option>';
                } else {
                    // 显示物流码选择
                    wlCodeGroup.style.display = 'block';

                    // 显示加载状态
                    wlCodeSelect.innerHTML = '<option value="">正在加载物流码...</option>';

                    // 获取国家代码
                    var countryCode = '{$row.ename}'; // 从模板变量获取国家代码

                    // 调用接口获取物流码
                    loadLogisticsCodes(selectedType, countryCode);
                }
            });
        }

        // 页面加载时检查是否已选择物流方式
        if (wlTypeSelect && wlTypeSelect.value !== '-1') {
            wlCodeGroup.style.display = 'block';
            var countryCode = '{$row.ename}';
            loadLogisticsCodes(wlTypeSelect.value, countryCode);
        }

        // 加载物流码的函数
        function loadLogisticsCodes(wlType, countryCode) {
            var xhr = new XMLHttpRequest();
            xhr.open('GET', 'getLogisticsCodes?wl_type=' + wlType + '&country_code=' + countryCode, true);
            xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            var ret = JSON.parse(xhr.responseText);
                            if (ret.code === 1 && ret.data) {
                                // 清空选项
                                wlCodeSelect.innerHTML = '<option value="">请选择物流码</option>';

                                // 添加物流码选项
                                ret.data.forEach(function(item) {
                                    var option = document.createElement('option');
                                    option.value = item.code;
                                    option.textContent = item.code + ' - ' + item.name;

                                    // 如果有当前值，设置为选中
                                    if (item.code === '{$row.wl_code}') {
                                        option.selected = true;
                                    }

                                    wlCodeSelect.appendChild(option);
                                });
                            } else {
                                var errorMsg = ret.msg || '暂无可用物流码';
                                wlCodeSelect.innerHTML = '<option value="">' + errorMsg + '</option>';
                                console.log('获取物流码失败:', ret);
                            }
                        } catch (e) {
                            wlCodeSelect.innerHTML = '<option value="">加载物流码失败</option>';
                        }
                    } else {
                        wlCodeSelect.innerHTML = '<option value="">加载物流码失败</option>';
                    }
                }
            };

            xhr.send();
        }

        // 绑定表单提交事件
        if (form) {
            form.addEventListener('submit', function(e) {
                // 阻止默认提交行为
                e.preventDefault();

                // 创建XMLHttpRequest对象
                var xhr = new XMLHttpRequest();

                // 设置请求
                xhr.open('POST', form.action || window.location.href, true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');

                // 处理响应
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        if (xhr.status === 200) {
                            try {
                                var ret = JSON.parse(xhr.responseText);
                                if (ret.code === 1) {
                                    // 成功回调
                                    if (parent && parent.Layer) {
                                        // 显示成功消息
                                        parent.Layer.msg(ret.msg || '绑定成功');

                                        // 刷新父页面表格
                                        if (parent.document.querySelector('.btn-refresh')) {
                                            parent.document.querySelector('.btn-refresh').click();
                                        }

                                        // 关闭当前窗口
                                        var index = parent.Layer.getFrameIndex(window.name);
                                        if (index) {
                                            parent.Layer.close(index);
                                        } else {
                                            parent.Layer.closeAll();
                                        }
                                    } else {
                                        alert(ret.msg || '绑定成功');
                                        window.close();
                                    }
                                } else {
                                    // 失败回调
                                    if (parent && parent.Layer) {
                                        parent.Layer.alert(ret.msg || '绑定失败');
                                    } else {
                                        alert(ret.msg || '绑定失败');
                                    }
                                }
                            } catch (e) {
                                alert('解析响应失败: ' + e.message);
                            }
                        } else {
                            alert('请求失败: ' + xhr.status);
                        }
                    }
                };

                // 获取表单数据
                var formData = new FormData(form);
                var params = [];
                for (var pair of formData.entries()) {
                    params.push(encodeURIComponent(pair[0]) + '=' + encodeURIComponent(pair[1]));
                }

                // 发送请求
                xhr.send(params.join('&'));

                return false;
            });
        }

        // 添加重置按钮事件
        var resetBtn = document.querySelector('.btn-default');
        if (resetBtn) {
            resetBtn.addEventListener('click', function(e) {
                e.preventDefault();
                if (form) {
                    form.reset();
                }
                return false;
            });
        }
    });
</script>
